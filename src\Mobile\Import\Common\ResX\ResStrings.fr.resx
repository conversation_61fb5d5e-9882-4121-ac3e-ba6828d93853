﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>Ajouter une actualite</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>Actualités</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Editer</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Detailles</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>Actualités</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Edition</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Retour a la liste</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>fr</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>Fr</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>Francais</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Largeur</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regions</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Creer une entree</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Zoom de la carte</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Centre de carte Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>Centre de carte X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Region dans l'app mobile</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Detailles</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Nouvelle entree</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Etes-vous sur de vouloir effacer cette entree?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>ou</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Ajouter une region</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Votre code de securite: </value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Panneau de controle Art of Foto</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Art of Foto: confirmez la creation de compte</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art of Foto a reçu une demande pour créer un compte d'utilisateur&lt;br&gt;
en utilisant votre adresse e-mail ({0}). &lt;br&gt;
&lt;br&gt;
Pour continuer à créer un compte en utilisant cette adresse e-mail, visitez le&lt;br&gt;
lien suivant: &lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="_blank" rel="noopener"&gt;{1}&lt;/a&gt;&lt;br&gt;
&lt;br&gt;
Si vous ne souhaitez pas créer de compte, ou si cette demande a été faite&lt;br&gt;
par erreur, vous pouvez tout simplement ignorer ce message. &lt;br&gt;
&lt;br&gt;
Si le lien ci-dessus ne fonctionne pas, ou si vous avez d'autres problèmes&lt;br&gt;
concernant votre compte, s'il vous plaît veuillez contacter l'administration&lt;br&gt;
à <EMAIL>.&lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Creation de compte</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>Nous vous avons envoyé un courrier électronique à l'adresse suivante: {0}.
S'il vous plaît veuillez suivre les instructions se trouvant dans cet e-mail pour finaliser la création de votre compte.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>L'heure</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Texte</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Parametres</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Image (url)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Auteur</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Redaction par</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Derniere redaction</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Hauteur d'image</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Largeur d'image</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Merci d'avoir confirmé votre courrier électronique. S'il vous plaît</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>cliquez ici pour vous identifier</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>S'enregistrer</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>Vous avez authentifié avec succès avec</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>S'il vous plaît veuillez entrer un nom d'utilisateur pour le site et cliquez sur le bouton S'enregistrer pour terminer l'enregistrement.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Enregistrement</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Associez votre compte {0}.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Connexion sans succès avec service.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Erreur d'authentification</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Connectez-vous</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>Ou utiliser un service externe pour se connecter</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Utilisez un compte local</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Se souvenir de moi</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>Entrer</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Créer un compte</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirmer le mot de passe</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Créer un nouveau compte</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Connectez-vous</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Bonjour, </value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(N'oubliez pas de se) Deconnecter</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>Infos</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>Sur la carte</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Centres</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Site web</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>Courrier</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Métro</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>Exporté par</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactif</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>Exporté</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Sous-titre</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Fichiers</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Télécharger une image</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Recherche par nom</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>Non authorisé</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Authorisé</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, nécessaire !</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>A exporter</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Vous souhaitez exporter ce record vers l'appl mobile</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Trier</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>Trier par Abc</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>Par date de changement</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>ERREUR: URL de l'image incorrect !</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Panneau de controle</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Exports</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Exporter:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Type d'export</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Erreur d'access</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>Il parait que vous n'avez pas le droit d'accéder à cette section. Veuillez contacter le support si vous pensez qu'il s'agit d'une erreur.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exportation</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Etes-vous sur de vouloir exporter?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Export effectué avec succes !</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>URL de base</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Liste des centres</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>dans la section</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tous</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>N'est pas a etre exporté</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>A bien été exporté</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>A etre exporté</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>dans la region de</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>par</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Categories</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Systeme</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Proprietaire</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Administrateur</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Redacteur</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Visiteur</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priorité</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>Par defaut</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Informations</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Sous-categories</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Categorie-parent, dans laquelle il faut inserer l'element courant</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Category de base</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>Baniere de News</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Category de base secondaire</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Télécharger une MINI-image</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>URL de l'image MINI</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Categorie</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Conseil Thalion</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>J'aime</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Unité</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Mots clefs</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Afficher</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Recherche..</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Toutes Categories</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>Par code</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>Par categorie</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Merchandiser</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Category de base 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Entrez la raison</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Image de l'anonce</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Corps</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Visage</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Notre selection</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>Mot de passe oublié?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>Traduction RU absente</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Not Found Error</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Erreur inconnue</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Liens Internet</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>Lien</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Total clicks</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>Etes-vous sur de vouloir effacer</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Traitement</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Traitements</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>Error: Please check requirements for fields below.</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Géstion</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Annuler les changement</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Retour sans sauvegarde</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Editor's notes, internal use only</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;Art of Foto&lt;/strong&gt; Panneau de controle</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>Me fixer?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Mot de passe retrouvé?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Reinitialiser le mot de passe</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Veuillez vérifier votre email pour réinitialiser votre mot de passe.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Mot de passe oublié</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Déjà enregistré?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Créer un compte</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>J'accepte &lt;strong&gt;les termes&lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>Vous devez accepter les termes et conditions.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>Le mot de passe et le mot de passe de confirmation ne correspondent pas.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>{0} doit etre long de {2} characteres minimum.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>Nom d'utilisateur ou mot de passe invalide.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>L'adresse e-mail '{0}' est déjà utilisée.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Réinitialisation de mot de passe</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Veuillez réinitialiser votre mot de passe en cliquant sur &lt;a href="{0}"&gt;ce lien&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Nouveau mot de passe</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Veuillez entrer votre nouveau mot de passe.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>Mot de passe ancient retrouvé?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Votre lien de reinitialisation a expiré.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Votre nouveau mot de passe a été défini.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Classe</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Privacy</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Publié le</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Access rapide avec</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>ou veuillez entrer vos details</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>Login avec reseau social</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Nean</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Changer le mot de passe</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Gerer votre compte</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Changer</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Authentification à deux facteurs</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Votre No de tel.</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Desactivé</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Gerer</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Desactiver</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>Skin</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Clef</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>Merci!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Merci d'avoir confirmé votre email. Vous pouvez maintenant utiliser vos infos d'identification pour vous connecter.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Votre compte n'a pas encore été attribué à un client Art of Foto existant. Veuillez contacter votre assistance technique.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>Rien n'a été trouvé !</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Panneau de controle pour clients</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Merci d'être patient. Nous travaillons sur le site qui sera bientôt de retour.</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>En construction</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Désolé, nous travaillons sur le site</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>Desktop</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contiens</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Elements des produits</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Composants</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Description RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Description EN</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Description FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>Ne sera pas exporté en dehors du panneau de contrôle si Oui</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>Vous pouvez télécharger un fichier ou entrer une URL existante dans le champ suivant</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>Sans actions</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Ouvrir le produit dans l'app</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Naviguer vers un lien web</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>La valeur du champ '{0}' doit etre unique</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Langues de contenue</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Entrez les codes de langue à 2 lettres</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Inconnu</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>A propos de nous</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Changer..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>Par date de sortie</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>Priorite dde triage</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>Par page</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Export rapide</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>Etez-vous sur de vouloir exporter cette section maintenant?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>L'exportation a été effectuée avec succes.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Chargement</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Messages push</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Vos actualités</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Naviguer vers un lien web avec navigateur interne</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Message simple</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Envoyer maintenant</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Sauvegarder sans envoyer</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Utilisateurs actifs</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Utilisateurs recents</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Utilisateurs non-actifs</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>Le texte ne peut pas être vide pour la langue anglaise.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Android</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Révélateur</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Total des appareils</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Sauvegarde</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Envoyé a {0} appareils</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Les messages Push n'ont pas encore été configurés pour vous.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Teneurs</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Paramètres generales du client</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Modifications enregistrées</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>Par region d'affichage</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Nombre total d'appareils messagables</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Nombre total d'installations</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Creez un mot de passe</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Votre code de securite est {0}. Utilisez le pour entrer dans le panneau de controle Art of Foto.</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Adresse email ou numéro de téléphone incorrect.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>Un utilisateur avec ce numéro de téléphone n'a pas été trouvé. Veuillez vous enregistrer.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Veuillez vérifier votre téléphone</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>Nous avons envoyé un code de vérification au numéro</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>Un utilisateur avec ce numéro de téléphone est déjà enregistré.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Code de vérification incorrect.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Confirmé</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>En attente de confirmation</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Refusé</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Système de réservation</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Horaires dispo</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Demandes de réservation</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Objets de reservation</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Creer un evenement</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Entrez le nom d'événement</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Glisser et déposer un événement sur le calendrier</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Erreur de connection</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Derniere version de l'appli mobile</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Version de l'appli mobile obsolète</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Debut</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Toute la journée</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 semaines</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Reservation prohibée</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>Etes-vous sur de vouloir effacer cet evenement?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Une seconde svp..</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Fiche d'evenement</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>confirmé</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>besoin de confimer</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Objet</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Categories des services</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nom long</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>MapX</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Clé unique requise, pour utilisation dans l'appli mobile.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>N'est pas actuellement utilisé.</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Liens internet ou code du produit, centre (champ "clef unique") etc..</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>Action a entreprendre en cas de click sur la news</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>Texte de la news</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Region culturel a la quelle montrer la news</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Image de l'annonce des news</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>Langue pour noms internationaux</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>Priorité de position en cas d'affichage dans uen liste</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Moduls actifs</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Deconnecter les utilisateurs</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Forcer tous les utilisateurs du panneau de controle relatifs a ce client de se reconnecter afin que les changements visuels prennent effet.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>Conseils d'utilisation</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Code de reference</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Plateforme</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>Le titre affiché</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>Le texte du message</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Nom du client dans le panneau de contrôle</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Couleur</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Prix</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Jours d'ouverture</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Clients</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>entrez par ici</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Equipes</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Gardiens de but</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Edition d'evenement</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Entreneurs</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Date de creation</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Classement</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>par classement</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>VKontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>Plus d'infos</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"par heur" etc..</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Details du prix</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Jours de semaine</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Non communique</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Sexe requis</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Niveau de difficulte</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Heur de début</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>Heur de fin</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Equipe</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Details d'evenement</value>
  </data>
  <data name="_Empty" xml:space="preserve">
    <value> </value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Evenements</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Elements des evenements</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organisations</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Organisation</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Type d'evenement</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Rassemblement</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Championnat</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Type de programme</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>Par jours de semaine</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>Avec dates fixes</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>Si avec des dates fixes, les jours de la semaine ne sont pas utilisés et vice versa.</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Horaires</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Deconnecter l'utilisateur</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Date de naissance</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Nom d'utilisateur valide requis</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Heurs d'ouverture fin</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Heurs d'ouverture</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Heurs d'ouverture debut</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Unconnue</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>En attente de confirmation</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>Confirmé</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>Refusé</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>Archivé</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Demande de réservation</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Lundi</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Dimanche</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Samedi</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Mercredi</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Mardi</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Jeudi</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Vendredi</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Heurs d'ouverture detaillées</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Confirmation des reservations automatique</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>Confirmer les reservations automatiquement</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Gallerie</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Votre nom</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>Reserver !</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Réservation en ligne</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Nom de famille</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Chaine</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Mise a jour des données..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>Pour les conditions données, il n'y a pas de temps disponible. Essayez de changer les conditions ci-dessous:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Oh la la!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Annulé</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>ID du client</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} vous attendra a {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>nous vous attendons a {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Veuillez attendre confirmation pour {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>En attente de confirmation</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Panneau</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Textes</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Réglages de l'Appli</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>Pas de temps disponible</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>Systeme de reservation uniquement</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Article</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>A voir egalement: </value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Masque des prix</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Apparence</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>Par notes</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>Nos contacts</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Trouver un itinéraire</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Objets</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Auhourdhui</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Demain</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>Dans {0} jours</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>Dans {0} jours</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>Dans {0} jours</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Authentification..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>Vous avez essayé trop de fois, veuillez réessayer dans {0} minutes.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>Échec d'enregistrement. Veuillez vérifier que vous avez fourni un numéro de téléphone valide ou réessayez plus tard.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>S'il vous plaît vérifier les données que vous avez entré sont valides.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>La réservation a échoué.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Vérification du code..</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>Nous vous avons envoyé un code de confirmation par SMS. Veuillez le saisir ci-dessous pour procéder à votre réservation:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>La réservation a échoué. Peut-être que quelqu'un a déjà pris ce temps, s'il vous plaît réessayez.</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>La verification du code a échoué.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Rechargement des donees..</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} a {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Code du SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Annulé</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Confirmé</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>En attente de confirmation</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Choix de la langue</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Cliquez ici ou glissez-déposez votre fichier ici.</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Chargement de l'image d'origine..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>Voir</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Sans description.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Galleries</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>Non visible dans l'application mobile, nom systèmу utilisé pour sélectionner cet élément dans des listes, etc.</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>Nous vous attendons {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>A {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Articles</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Ouvrir l'article de blog</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Temps de publication</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Logo splashscreen</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Image Societe</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Pour Nos Contacts</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>Simple</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>Difficile</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Tres difficile</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Reponces</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Reponce</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Correcte</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Questions</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>Par niveau de difficulte</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>Liens d'image de QR Code</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Quizz</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>Temps pour une question</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>Quizzes</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>Temps pour toutes les questions en secs</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Brands</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Actions Promo</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Inclure avec tags</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Declure avec tags</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Mots-clefs pour recherche de cet element</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Veuillez sauvegarder cet element pour pouvoir y ajouter des sous-elements.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>Recompences</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Pourcentage reponces corrects</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Remise</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Action Promo</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Etat</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>A venir</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Fermé</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Sortie</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Chargement..</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>secondes</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>secondes</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>secondes</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>secondes</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>secondes</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>Succes</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>Pourcentage de coupon</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>Liens plus d'infos</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prenom</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>Total questions a afficher</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>* - pour toutes les questions ou autres tags..</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>Ouvrir l'action promo dans l'app</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>Total des prix</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>Prix laissés</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>Network Error</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>Unknown Error</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>Denied</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>Total connexions</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Requete</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Requetes</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Ordre</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>Conexions approuvees</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>Expired</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>A propos...</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>Actualités</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Trouver un centre</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Actualités</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>Votre salon</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>   </value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Changer de region</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Trouver un itinéraire</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Mon salon</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>Où trouver nos centres</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Erreur de connexion. Veuillez réessayer ultérieurement.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Erreur de connexion. Veuillez réessayer ultérieurement.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Erreur de connexion. Veuillez réessayer ultérieurement.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Erreur de connexion. Veuillez vérifier vos paramètres Internet et réessayez ultérieurement..</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>Mon сentre favoris</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>Bienvenue !</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>Erreur de connexion à internet.
Veuillez vérifier vos paramètres.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Le programme externe est nécessaire pour la navigation.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Aller sur le site</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>Sur la carte</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Appeler</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>Ajoutez le aux favoris pour un accès rapide !</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Ajouter aux favoris</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Reconnexion</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Nous trouver</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>Voulez-vous vraiment supprimer ce favoris ?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Retirer des favoris</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Vous avez désormais un accès rapide aux donnée de ce centre via la section des Favoris.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>A propos du centre</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>Le programme a besoin d'accéder à votre position GPS pour pouvoir nous trouver. Activer l'accès maintenant?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Votre module GPS est éteint, veuillez l'activer que nous soyons mesure de vous aider avec la geoposition.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>Par le métro :</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Informations</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contactez-nous</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Voir la carte</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto et les propriétaires de contenu respectifs</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Définition de votre position..</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>Région</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Pour les centres</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Pour les spécialistes</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Login pour partenaires</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>Comment nous trouver</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>Vous pouvez ajouter votre centre préféré à cette page pour un accès rapide.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Trouver un itinéraire</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>Remplacer le favoris actuel?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>Vers la liste des centres..</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Trouvez votre salon</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>Félicitations!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Cool alors</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Erreur de connexion. Veuillez réessayer ultérieurement.</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Centres</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>A propos de nous</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>Région</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Paramètres </value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Interface</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>Menu de bas sans texte</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Afficher la page Mon salon (Favoris) au lancement du programme</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Page d'acceuil</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Désactiver les animations de fond pour économiser la batterie</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Retour à la liste</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Afficher les fiches de bienvenue à chaque demarrage du programme</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>Et encore..</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Revoir les fiches de bienvenue</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>PROCEDER</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>Nous avons cree une nouvelle version programme, s'il vout plait veuillez la mettre à jour !</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>A bientôt !</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Messages push sans signal sonore</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>Cacher le message de bienvenu?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>Trouvez</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>Ajoutez</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Cliquez deux fois</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>Suivez</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>sur l'icône de la section pour revenir à son titre</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>Trier par Km</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>Sur la carte</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>ver.</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Catalogue de produits</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Sous-categories:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>Touts produits dans la categorie</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>CONSEIL THALION</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Resultats de recherche</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>lire la suite</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Recherche de produits</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Recherche</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>A VOIR ABSOLUMENT</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>Consultez</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>Vous avez cherché</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>Veuillez entrer plus de caractères !</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Recherche de centres</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>Paramètres système</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Plus tard</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>Activer le GPS</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>Trié par distance</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>Trié par alphabet</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>PROMOTIONS</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>Les produits du catalogue peuvent être ajoutés à la Liste de souhaits.
La liste est utile pour faire du shopping dans votre centre de beauté, ou à partager avec votre esthéticienne ou vos amis.</value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>Liste de souhaits</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>A propos de nous..</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Demander confirmation avant de  supprimer un element d'une liste</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>AUTRES CATEGORIES</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Aller dans le catalogue</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Partager</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>ALLEZ DANS LA CATEGORIE</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Ref.</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>Vers le catalogue</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>Ma Liste de souhaits</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Effacer la liste</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>Nos produits de la gamme personnelle sont en vente que dans des centres certifiés par THALION.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>Si votre centre THALION ne possède pas tous les produits que vouz voulez en stock, s’il vous plait veuillez nous contacter et nous pourrons vous aider.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Ou trouver</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>Nous contacter</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Supprimer de la liste de souhaits?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>Etes-vous sûr de vouloir effacer votre Liste de souhaits?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>Pour être en mesure de calculer les distances, nous aurions besoin de vos cordonnées.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>Vouz avez selectionné {0} {1}.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>produits</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>produit</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>produits</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>produits</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>produits</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>En savoir plus..</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Ajouté à la liste de souhaits</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Appuiyez encore une fois pour sortir du programme</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>Vers le catalogue..</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>Vers l'index</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Ou trouver</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>A gauche</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>Et encore..</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>A VOIR EGALEMENT</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Catalogue de produits</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Favoris</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>Mes favoris</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>Si vous souhaitez que nous puissions trouver les centres THALION les plus proches repondez positif à la fenêtre suivante.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Salut</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Vérifier les paramètres</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Traitement de votre réservation ..</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>ms</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>.</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>Son</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>Entrée de l'heur dans le temps</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>Minuteur pour {0}</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>Minuteur a fini pour {0}</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>Soufflet</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>Extension du soufflet</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>Ici vous pouvez obtenir une valeur précise pour régler votre f/stop en utilisant une caméra à soufflet</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>Temps</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>Calculateur du temps</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>parties</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>ml</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 mm</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>Convertir 35mm</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>Ici vous pouvez obtenir la distance focale de votre objectif exprimée en équivalent 35 mm.</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>Vous pouvez basculer entre mm et pouces en cliquant sur le texte correspondant à droite du champ de saisie.</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>Vous pouvez basculer entre mm et pouces en cliquant sur le texte correspondant à droite du champ de saisie.</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>Révélateur</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>Mixer un révélateur</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>Format du cadre:</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>Dans le volume</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>À partir de</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>Résultat (ml.)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>Partie A</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>Partie B</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>De l'eau</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>Le calcul des composants du développeur, basé sur les volumes spécifiés.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>Sélection automatique des volumes pour obtenir la quantité spécifiée de révélateur.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>Converti</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/Stop</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C - appuyez une fois pour réinitialiser la valeur actuelle, deux fois pour une réinitialisation complète
% - utilisé en combinaison avec l'opération précédemment entrée.
Par exemple: appuyez sur + puis sur %, puis entrez un nombre décimal.
Résultat: vous avez ajouté les pourcentages que vous venez d'entrer à l'heure existante.</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>Votre résultat est {0:0.00}</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>Distance focale</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Choisissez vos onglets  ({0}/{1} min {2})</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Sélection d'onglets à afficher</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Thème</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>Dark</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>Light</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>Le projet Art of Foto a été fondé début 2011. L'idée de base la galerie a été de préserver le patrimoine photographique Russe, bien que de soutenir la photographie analogique contemporaine et de l'aider à se développer, tant sur le plan artistique que sur le plan technologique. 

Nous avons ouvert la galerie Art of Foto, une chambre noire et un studio photo grand format à Saint-Pétersbourg en 2015.

La galerie Art of Foto est une collection de photographies ayant une valeur historique et artistique. À l'heure actuelle, nous avons des œuvres de grands maîtres tels que Valery Plotnikov, Boris Smelov, John Sexton, Leonid Bogdanov, Valentin Samarin, John Wimberly, Robert Doisneau et d'autres.

L'objectif principal de la galerie est de promouvoir les photographes russes talentueux, à la fois chez eux et à l'étranger. Nous soutenons activement les photographes de Saint-Pétersbourg et de Moscou dont les œuvres sont susceptibles de s’ajouter au patrimoine artistique russe.

Les membres de l’Art of Foto Artists’ Union organisent chaque année en Russie et en Europe des expositions de photographies noir et blanc imprimées à la main dans le but de créer une image positive de la photographie russe traditionnelle et contemporaine parmi les connaisseurs et les experts du monde entier.</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>App développé par AppoMobi</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, diagonal de {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>Solution prête</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>Vous aimez {0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>Merci pour vos commentaires, nous allons essayer d'améliorer notre application!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>Merci de nous évaluer sur GooglePlay, nous vous en serons très reconnaissants!</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Obligatoire</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>j</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Gérer vos logins externes</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Pause fin</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Début de la pause</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Explicite temps ré...</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Si temps ré... doit être indiquée dans le temps de réserver en ligne pour chaque objet à être disponible</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>Millimètres</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>Pouces</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>Choisir les unités</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Sur...</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konyushennaya Street, 1, Sankt-Peterburg, Russie, 191186</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>Bienvenue</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>Effondrement</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>Développez</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>Il s’agit d’une aide de module développeur.</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>Pas de filtre</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Dépassement</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>Exposition corrigée</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>Mins</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>Secs</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>Données de 2016
Le fabricant recommande de faire une correction lors de l’élaboration :
02:00 %
-&amp;gt; 50 sec : 20 %
-&amp;gt; 20 min : 30 %</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>Manger des trucs</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>Formule non communiquée par le producteur...</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>Cette durée de développement n’est pas recommandée par le fabricant.</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>Calcul de l’exposition, compte tenu de l’influence de l’effet Schwarzschild ("Reciprocity effect")</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>Reciprocity</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>ATTENTION

La valeur des modifications lorsque vous photographiez avec le filtre par le fabricant et le type était en moyenne d’éclairage. 

Vous recommandons de tester les filtres et le film pour obtenir un résultat stable et précis.</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>En raison de l’absence de données du fabricant, on utilise notre propre formule</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>Selon le producteur la correction n’est pas nécessaire dans ce diapason</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>Nos actualités</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>Données de 2002
Selon le fabricant, il n’est pas nécessaire de faire des ajustements au-dessous d'une seconde, pour plus d'une seconde nous utilisons notre propre formule</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>La caméra est conçue pour visualiser les négatifs en temps réel. Vous pouvez changer le filtre, la caméra et enregistrer le cadre dans la galerie.</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>Caméra négative</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Caméra</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>Ce module ne peut pas fonctionner sans autorisations. Veuillez autoriser l&amp;#39;application dans les paramètres système ou désinstaller l&amp;#39;application et l&amp;#39;installer à partir de zéro pour obtenir à nouveau la demande d&amp;#39;autorisations système.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>Aucune autorisation</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>Viseur</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>Viseur</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Sélection</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Appliquer</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>Objectifs pour "{0}"</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>Changer de format</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>Modifier les préréglages</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>Préréglage</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>Films</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filtres</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>Ce module est conçu pour la simulation approximative de viseurs analogiques. Vous pouvez zoomer sur l'écran avec vos doigts. Les valeurs vertes peuvent être tapées.</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>Aucun objectif ajouté</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>Ajouter un objectif (mm)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>Écran toujours allumé</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>Ajustement</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>Autorisations requises pour géolocaliser les photos</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Utiliser le dossier Art Of Foto</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>Ouvrir</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>Géotag Pictures</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Reconnexion</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>Table de développement</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>Développement</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>Exposition</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>Ouverture</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>Obturateur</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Réessayez</value>
  </data>
  <data name="ExposureMeter" xml:space="preserve">
    <value>Posemètre</value>
  </data>
</root>