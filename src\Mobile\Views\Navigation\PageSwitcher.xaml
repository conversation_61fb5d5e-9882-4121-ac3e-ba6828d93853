﻿<?xml version="1.0" encoding="utf-8" ?>
<views3:BasePage
    x:Class="AppoMobi.Mobile.Views.Navigation.PageSwitcher"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="using:AppoMobi"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:main="clr-namespace:AppoMobi.Main"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mobile="clr-namespace:AppoMobi.Mobile"
    xmlns:navigation="clr-namespace:AppoMobi.Mobile.Views.Navigation"
    xmlns:navigation1="clr-namespace:AppoMobi.ViewModels.Navigation"
    xmlns:strings="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:tabs="clr-namespace:AppoMobi.Framework.Maui.Controls.Navigation.Tabs;assembly=AppoMobi.Framework.Maui"
    xmlns:touch="clr-namespace:AppoMobi.Framework.Maui.Touch;assembly=AppoMobi.Framework.Maui"
    xmlns:views3="clr-namespace:AppoMobi.Views"
    xmlns:xam11="using:AppoMobi.Xam"
    x:Name="ThisPage"
    ios:Page.UseSafeArea="False"
    x:DataType="navigation1:TabsViewModel"
    BackgroundColor="{x:Static xam11:BackColors.PageT}"
    HideSoftInputOnTapped="True"
    NavigationPage.HasNavigationBar="false"
    mc:Ignorable="d">

    <ContentPage.Resources>
        <ResourceDictionary>


            <tabs:ObservableViews x:Key="ViewsForUser">

                <!--  TODO disable scroll for calc  -->
                <navigation:LazyPageWithIncludedContent x:TypeArguments="main:ContentTimeCalculator" />
 
            </tabs:ObservableViews>
 

        </ResourceDictionary>
    </ContentPage.Resources>

    <ContentPage.Content>

        <tabs:ViewsContainer
            x:Name="Container"
            Padding="0"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>


            <draw:Canvas
                x:Name="BackgroundCanvas"
                BackgroundColor="{x:Static xam11:BackColors.Page}"
                HorizontalOptions="Fill"
                IsVisible="False"
                VerticalOptions="Fill">
                <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">
                    <draw:SkiaLayout.Background>
                        <LinearGradientBrush EndPoint="0,1">
                            <GradientStop Offset="0.0" Color="{x:Static xam11:BackColors.GradientPageFaderStartEnd}" />
                            <GradientStop Offset="1.0" Color="{x:Static xam11:BackColors.GradientPageFaderStart}" />
                        </LinearGradientBrush>
                    </draw:SkiaLayout.Background>

                    <draw:SkiaSvg
                        HeightRequest="110"
                        HorizontalOptions="Center"
                        LockRatio="1"
                        Opacity="0.25"
                        SvgString="{StaticResource SvgLogo}"
                        VerticalOptions="Center" />

                </draw:SkiaLayout>
            </draw:Canvas>


            <!--  PAGES SWITCHER  -->
            <tabs:ViewSwitcher
                x:Name="Switcher"
     
                Margin="0"
                AnimateTabs="True"
                AnimationEasing="{x:Static Easing.SinOut}"
                AnimationSpeed="260"
                CurrentPageChanged="OnCurrentPageChanged"
                HasPseudoTabs="True"
                HorizontalOptions="FillAndExpand"
                IsClippedToBounds="True"
                SelectedIndex="{Binding SelectedIndex, Mode=TwoWay}"
                VerticalOptions="Fill" />

            <touch:PanAwareHotspot
                x:Name="LeftHotspot"
                Grid.RowSpan="2"
                Margin="0,150,0,150"
                HorizontalOptions="Start"
                Panned="PannedRight"
                Swiped="SwipedRight"
                TranslationX="{Binding Source={x:Reference LeftHotspot}, Path=DistanceX}"
                VerticalOptions="FillAndExpand"
                WidthRequest="24" />

            <touch:PanAwareHotspot
                x:Name="RightHotspot"
                Grid.RowSpan="2"
                Margin="0,150,0,150"
                HorizontalOptions="End"
                Panned="PannedLeft"
                Swiped="SwipedLeft"
                TranslationX="{Binding Source={x:Reference RightHotspot}, Path=DistanceX}"
                VerticalOptions="FillAndExpand"
                WidthRequest="24" />

        </tabs:ViewsContainer>


    </ContentPage.Content>
</views3:BasePage>