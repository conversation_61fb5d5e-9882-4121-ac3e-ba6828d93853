﻿using System.Diagnostics;
using LayoutAlignment = Microsoft.Maui.Primitives.LayoutAlignment;

namespace AppoMobi.Main
{
    public class LightPad : AppScreen
    {
        public LightPad()
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    VerticalOptions = LayoutOptions.Center,
                    Type = LayoutType.Column,
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaLabel(
                            "Модуль предназначен для..... При открытом столе нажимайте на область в правом верхнем углу для показа и скрытия настроек.")
                        {
                            WidthRequest = 250,
                            HorizontalOptions = LayoutOptions.Center,
                            UseCache = SkiaCacheType.Operations,
                            FontFamily = "FontText",
                            //TextColor = Colors.Black,
                            //FontSize = 80
                        },
                        new AppButton(ResStrings.BtnOpen)
                        {
                            UseCache = SkiaCacheType.Image,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = 16
                        }.OnTapped((me) =>
                        {
                            Debug.WriteLine("OPEN");

                            var wrapper = new ScreenCanvas()
                            {
                                RenderingMode = RenderingModeType.Accelerated,
                                Content = new LightPadPopup()
                            };

                            var popup = new CustomPopup()
                            {
                                HorizontalOptions = LayoutAlignment.Fill,
                                VerticalOptions = LayoutAlignment.Fill,
                                IgnoreSafeArea = true,
                                Content = new DisposableContent()
                                {
                                    Content = wrapper
                                }
                            };

                            MainThread.BeginInvokeOnMainThread(() => { popup.Open(true); });
                        })
                    }
                }
            };
        }
    }
}
