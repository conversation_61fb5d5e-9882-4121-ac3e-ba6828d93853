﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Maui.Navigation;
using AppoMobi.Models;
using AppoMobi.Services;
using AppoMobi.Touch;
using AppoMobi.Xam;
using DrawnUi.Draw;
using System;
using System.Diagnostics;
using System.Threading.Tasks;


namespace AppoMobi.UI
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NavBar
    {
        public NavBar()
        {
            InitializeComponent();
            Initialize();

            //PopupSearchHide();
            SetTitleImage("");

            SetupButtons();
        }

        public void SetTitleImage(string image)
        {
            TitleLogo.Source = image;// "resource://AppoMobi.Mobile.Images.Brand.logoheart1024.png";
        }

        /// <summary>
        /// You have to call it manually when device orientation changes
        /// </summary>
        public double Update(DeviceRotation Orientation)
        {
            Margin = new Thickness(0, 0, 0, 0);
            double value = AppUI.NavBarHeight;

            HeightRequest = value;
            cTitleBarMain.HeightRequest = value;
            txtTitle.HeightRequest = value;

            return value;

            if (Settings.Current.iModel == "iPhone X")//iphoneX
            {
                if (Orientation == DeviceRotation.Landscape)
                {
                    double marginX = 0;
                    Margin = new Thickness(0, 0, 0, 0);
                }
                else
                {
                    double marginX = 12.0;
                    Margin = new Thickness(0, 20 + marginX, 0, 0);
                }
            }
            else
            {
                HeightRequest = AppUI.NavBarHeight;
                Margin = new Thickness(0, Super.StatusBarHeight, 0, 0);
                cTitleBarMain.HeightRequest = AppUI.NavBarHeight;
            }

            return value;
        }

        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            if (AppUI.NavBarFontBold)
                txtTitle.FontAttributes = FontAttributes.Bold;



        }

        private double _ScreenWidth;
        public double ScreenWidth
        {
            get
            {
                return Width;
                //return _ScreenWidth;
            }
            set
            {
                if (_ScreenWidth != value)
                {
                    _ScreenWidth = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _IsPopupSearchVisible;
        public bool IsPopupSearchVisible
        {
            get { return _IsPopupSearchVisible; }
            set
            {
                if (_IsPopupSearchVisible != value)
                {
                    _IsPopupSearchVisible = value;
                    OnPropertyChanged();
                }
            }
        }

        private const double popupSearchOffset = 16 + 23 + 16 - 4;
        public const uint PopupOptionsTimeIn = 300;
        public const uint PopupOptionsTimeOut = 300;
        public const uint PopupShadowTime = 100;

        public void ToggleButtonVisibility(ButtonType button, bool visible)

        {
            switch (button)
            {

            case ButtonType.Left1:
            LeftIcon1.IsVisible = visible;
            LeftIcon1txt.IsVisible = visible;
            //hsLeftIcon1.IsVisible = visible;
            break;
            case ButtonType.Left2:
            LeftIcon2.IsVisible = visible;
            LeftIcon2txt.IsVisible = visible;
            hsLeftIcon2.IsVisible = visible;
            break;

            case ButtonType.Right1:
            RightIcon1.IsVisible = visible;
            RightIcon1txt.IsVisible = visible;
            //hsRightIcon1.IsVisible = visible;
            break;
            case ButtonType.Right2:
            RightIcon2.IsVisible = visible;
            RightIcon2txt.IsVisible = visible;
            //hsRightIcon2.IsVisible = visible;
            break;
            }

            SetupButtons();
        }

        public void SetupButtons()
        {
            if (LeftIcon1.IsVisible)
            {
                MainThread.BeginInvokeOnMainThread(() => { LeftIcon1txt.IsVisible = LeftIcon1.Source == null; });
            }
        }


        private bool lock_down { get; set; }

        public void AnimateIcon()

        {
            if (lock_down) return;
            lock_down = true;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                try
                {
                    // imgFx.FadeTo(0.75, 75);
                    await RightIcon1.ScaleTo(1.75, 75);
                    //await RightIcon1.ScaleTo(1.0, 75);
                    //await RightIcon1.ScaleTo(1.25, 75);
                    await RightIcon1.ScaleTo(1.0, 275); lock_down = false;
                }
                catch (Exception e)
                {
                }
            });

        }


        public async Task ToggleSearch()

        {
            if (!IsPopupSearchVisible) await PopupSearchShow();
            else await PopupSearchHide();
        }

        public async Task PopupSearchInit(string message = null)
        {
            //var r = new Rect(cTitleBarMain.X + cTitleBarMain.Width, cTitleBarMain.Y, cTitleBarMain.Width - popupSearchOffset, cTitleBarMain.Height);
            //Microsoft.Maui.Controls.AbsoluteLayout.SetLayoutBounds(cTitleBarSearch, r);

            /*
            if (!cTitleBarSearch.IsVisible)
            {
                cTitleBarSearch.IsVisible = true;
            }
            else
            {
                cTitleBarSearch.IsVisible = false;
            }
            */
        }

        private async Task PopupSearchShow()

        {
            await PopupSearchInit();
            try
            {
                await cNavBarSlider.TranslateTo(-cTitleBarMain.Width + popupSearchOffset, 0, PopupOptionsTimeIn, Easing.CubicInOut);
            }
            catch (Exception e)
            {
            }
            IsPopupSearchVisible = true;

            //ControlSearchEntry.Focus();
        }

        private async Task PopupSearchHide(bool animate = true)

        {
            //ControlSearchEntry.Unfocus();

            //await PopupOptionsInit(_model.StatusDesc);
            uint d = PopupOptionsTimeOut;
            if (!animate)
            {
                d = 0;
            }

            try
            {
                await cNavBarSlider.TranslateTo(0, 0, d, Easing.CubicInOut);
            }
            catch (Exception e)
            {
            }
            IsPopupSearchVisible = false;
        }


        public string SearchTerm { get; set; }

        #region Properties




        // SearchPlaceholder

        private const string nameSearchPlaceholder = "SearchPlaceholder";
        public static readonly BindableProperty SearchPlaceholderProperty = BindableProperty.Create(nameSearchPlaceholder, typeof(string), typeof(NavBar), ResStrings.EnterString); //, BindingMode.TwoWay
        public string SearchPlaceholder
        {
            get { return (string)GetValue(SearchPlaceholderProperty); }
            set { SetValue(SearchPlaceholderProperty, value); }
        }



        // LeftIcon2Source

        private const string nameLeftIcon2Source = "LeftIcon2Source";
        public static readonly BindableProperty LeftIcon2SourceProperty = BindableProperty.Create(nameLeftIcon2Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon2Source
        {
            get { return (ImageSource)GetValue(LeftIcon2SourceProperty); }
            set { SetValue(LeftIcon2SourceProperty, value); }
        }


        // RightIcon2Source

        private const string nameRightIcon2Source = "RightIcon2Source";
        public static readonly BindableProperty RightIcon2SourceProperty = BindableProperty.Create(nameRightIcon2Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource RightIcon2Source
        {
            get { return (ImageSource)GetValue(RightIcon2SourceProperty); }
            set { SetValue(RightIcon2SourceProperty, value); }
        }


        //-------------------------------------------------------------
        // RightIcon2Symbol
        //-------------------------------------------------------------
        private const string nameRightIcon2Symbol = "RightIcon2Symbol";
        public static readonly BindableProperty RightIcon2SymbolProperty = BindableProperty.Create(nameRightIcon2Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel RightIcon2Symbol
        {
            get { return RightIcon2txt; }
        }

        //-------------------------------------------------------------
        // RightIcon1Symbol
        //-------------------------------------------------------------
        private const string nameRightIcon1Symbol = "RightIcon1Symbol";
        public static readonly BindableProperty RightIcon1SymbolProperty = BindableProperty.Create(nameRightIcon1Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel RightIcon1Symbol
        {
            get { return RightIcon1txt; }
        }

        //-------------------------------------------------------------
        // LeftIcon2Symbol
        //-------------------------------------------------------------
        private const string nameLeftIcon2Symbol = "LeftIcon2Symbol";
        public static readonly BindableProperty LeftIcon2SymbolProperty = BindableProperty.Create(nameLeftIcon2Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel LeftIcon2Symbol
        {
            get { return LeftIcon2txt; }
        }

        //-------------------------------------------------------------
        // LeftIcon1Symbol
        //-------------------------------------------------------------
        private const string nameLeftIcon1Symbol = "LeftIcon1Symbol";
        public static readonly BindableProperty LeftIcon1SymbolProperty = BindableProperty.Create(nameLeftIcon1Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel LeftIcon1Symbol
        {
            get { return LeftIcon1txt; }
        }



        // RightIcon1Source

        private const string nameRightIcon1Source = "RightIcon1Source";
        public static readonly BindableProperty RightIcon1SourceProperty = BindableProperty.Create(nameRightIcon1Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource RightIcon1Source
        {
            get { return (ImageSource)GetValue(RightIcon1SourceProperty); }
            set { SetValue(RightIcon1SourceProperty, value); }
        }


        // LeftIcon1Source

        private const string nameLeftIcon1Source = "LeftIcon1Source";
        public static readonly BindableProperty LeftIcon1SourceProperty = BindableProperty.Create(nameLeftIcon1Source, typeof(ImageSource), typeof(NavBar), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon1Source
        {
            get { return (ImageSource)GetValue(LeftIcon1SourceProperty); }
            set { SetValue(LeftIcon1SourceProperty, value); }
        }



        // Title

        private const string nameTitle = "Title";

        public static readonly BindableProperty TitleProperty = BindableProperty.Create(nameTitle, typeof(string),
            typeof(NavBar), string.Empty, BindingMode.TwoWay);
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }


        // FontSize

        private const string nameFontSize = "FontSize";

        public static readonly BindableProperty FontSizeProperty = BindableProperty.Create(nameFontSize, typeof(double), typeof(NavBar), 1.0d);
        public double FontSize
        {
            get { return (double)GetValue(FontSizeProperty); }
            set { SetValue(FontSizeProperty, value); }
        }

        // FontFamily

        private const string nameFontFamily = "FontFamily";
        public static readonly BindableProperty FontFamilyProperty = BindableProperty.Create(nameFontFamily, typeof(string), typeof(NavBar), string.Empty); //, BindingMode.TwoWay
        public string FontFamily
        {
            get { return (string)GetValue(FontFamilyProperty); }
            set { SetValue(FontFamilyProperty, value); }
        }

        // TextColor

        private const string nameTextColor = "TextColor";
        public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameTextColor, typeof(Color), typeof(NavBar), Colors.Black); //, BindingMode.TwoWay
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }

        // FontAttributes

        private const string nameFontAttributes = "FontAttributes";
        public static readonly BindableProperty FontAttributesProperty = BindableProperty.Create(nameFontAttributes, typeof(FontAttributes), typeof(NavBar), new FontAttributes()); //, BindingMode.TwoWay
        public FontAttributes FontAttributes
        {
            get { return (FontAttributes)GetValue(FontAttributesProperty); }
            set { SetValue(FontAttributesProperty, value); }
        }

        #endregion

        #region TouchHandlers

        public event EventHandler OnDoubleTapped = null;
        private void OnDoubleTapped_TitleBar(object sender, TapEventArgs e)
        {
            OnDoubleTapped?.Invoke(this, e);
        }

        CancellationTokenSource _scaleCancelTokenSource;

        private async Task AnimateIcon(SkiaControl icon)
        {
            try
            {
                _scaleCancelTokenSource?.Cancel();
                var cancel = new CancellationTokenSource();
                _scaleCancelTokenSource = cancel;

                var initial = 1.0f;//icon.Scale;

                await icon.ScaleToAsync(initial * 0.8, initial * 0.8, 40, null, cancel);
                await icon.ScaleToAsync(initial * 1.35, initial * 1.35, 125, null, cancel);
                await icon.ScaleToAsync(initial, initial, 200, Easing.SpringIn, cancel);
                await Task.Delay(10);
            }
            catch (Exception e)
            {
            }
        }

        private async Task AnimateIcon(View icon)

        {
            try
            {
                var initial = icon.Scale;
                await icon.ScaleTo(initial * 0.8, 40);
                await icon.ScaleTo(initial * 1.35, 125);
                await icon.ScaleTo(initial, 200, Easing.SpringIn);
                await Task.Delay(10);
            }
            catch (Exception e)
            {
            }
        }

        public event EventHandler OnDownRightIcon1 = null;
        private void OnDown_RightIcon1(object sender, DownUpEventArgs e)
        {
            if (lock_down) return;
            if (RightIcon1.IsVisible)
            {
                if (RightIcon1.Source != null)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(RightIcon1);
                        lock_down = false;
                        OnDownRightIcon1?.Invoke(this, e);
                    });
                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(RightIcon1txt);
                        lock_down = false;
                        OnDownRightIcon1?.Invoke(this, e);
                    });
                }
            }
        }

        public event EventHandler OnDownRightIcon2 = null;
        private void OnDown_RightIcon2(object sender, DownUpEventArgs e)

        {
            if (lock_down) return;
            if (RightIcon2.IsVisible)
            {
                if (RightIcon2.Source != null)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(RightIcon2);
                        lock_down = false;
                        OnDownRightIcon2?.Invoke(this, e);
                    });
                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(RightIcon2txt);
                        lock_down = false;
                        OnDownRightIcon2?.Invoke(this, e);
                    });

                }
            }
        }

        public event EventHandler OnDownLeftIcon1 = null;
        private void OnDown_LeftIcon1(object? sender, DownUpEventArgs e)

        {
            if (lock_down) return;
            if (LeftIcon1.IsVisible)
            {
                if (LeftIcon1.Source != null)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(LeftIcon1);
                        lock_down = false;
                        OnDownLeftIcon1?.Invoke(this, e);
                    });
                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(LeftIcon1txt);
                        lock_down = false;
                        OnDownLeftIcon1?.Invoke(this, e);
                    });
                }
            }
        }

        public event EventHandler OnDownLeftIcon2 = null;
        private void OnDown_LeftIcon2(object sender, DownUpEventArgs e)

        {
            if (lock_down) return;
            if (LeftIcon2.IsVisible)
                if (LeftIcon2.Source != null)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(LeftIcon2);
                        lock_down = false;
                        OnDownLeftIcon2?.Invoke(this, e);
                    });
                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        lock_down = true;
                        await AnimateIcon(LeftIcon2txt);
                        lock_down = false;
                        OnDownLeftIcon2?.Invoke(this, e);
                    });
                }
        }

        private void OnTapped_SearchIcon(object sender, DownUpEventArgs e)

        {
            ToggleSearch();
        }


        private void OnDown_TitleBar(object sender, DownUpEventArgs e)

        {
            //todo
        }



        #endregion

        #region EventHandlers

        public EventHandler<MessageEventArgs> SearchExecute { get; set; }
        public EventHandler SearchReset { get; set; }

        #endregion



        // TitleImage

        private const string nameTitleImage = "TitleImage";
        public static readonly BindableProperty TitleImageProperty = BindableProperty.Create(nameTitleImage, typeof(string), typeof(NavBar), ""); //, BindingMode.TwoWay
        public string TitleImage
        {
            get { return (string)GetValue(TitleImageProperty); }
            set { SetValue(TitleImageProperty, value); }
        }

        // DO NOT CLOSE

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)

        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {

            //property changed
            case nameTitle:
            txtTitle.Text = Title;
            break;

            case nameTitleImage:
            SetTitleImage(TitleImage);
            break;

            case nameTextColor:
            txtTitle.TextColor = TextColor;
            break;
            case nameFontAttributes:
            txtTitle.FontAttributes = FontAttributes;
            break;

            case nameFontFamily:
            txtTitle.FontFamily = FontFamily;
            break;

            case nameFontSize:
            txtTitle.FontSize = FontSize;
            break;

            case nameRightIcon1Source:
            RightIcon1.Source = RightIcon1Source;
            break;
            case nameRightIcon2Source:
            RightIcon2.Source = RightIcon2Source;
            break;

            case nameLeftIcon1Source:
            LeftIcon1.Source = LeftIcon1Source;
            break;
            case nameLeftIcon2Source:
            LeftIcon2.Source = LeftIcon2Source;
            break;


            }

        }


        public event EventHandler<MessageEventArgs> SearchCanceled;
        public event EventHandler<MessageEventArgs> SearchChanged;

        public void AnimateLogoImage()
        {

        }
    }



}
