﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>添加新闻</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>新闻</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>细节</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>总计</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>新闻</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>联系</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>返回列表</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>cn</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>Cn</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>简体中文</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>宽度</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>区域</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>编码</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>地图缩放</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>地图中心Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>地图中心X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>移动应用中的区域</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>细节</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>新进入</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>您确认要删除吗?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>或</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>添加地址</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>您的安全代码:</value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Art of Foto 控制面板</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Art of Foto: 确认帐户创建</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art of Foto已收到创建用户帐户的请求&lt;br&gt;使用您的电子邮件地址({0}).&lt;br&gt; &lt;br&gt;要使用此电子邮件地址继续创建帐户，请访问:&lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="_blank" rel="noopener"&gt;{1}&lt;/a&gt;&lt;br&gt;如果您不想创建帐户，或者此请求是在&lt;br&gt;中进行的
错误你可以忽略这条消息。&lt;br&gt;
&lt;br&gt;如果上述链接不起作用，或者您有任何其他问题&lt;br&gt;
您的帐户，请通过***************联系管理部门。&lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>帐户创建</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>帐户创建</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>区域</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>时间</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>文本</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>行动</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>参数</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>图像(网址)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>作者</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>最新编辑</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>图像高度</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>图像宽度</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>感谢您确认电子邮件。请</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>点击此处登录</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>您已成功通过身份验证</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>请在下面输入此站点的用户名，然后单击“注册”按钮完成登录。</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>关联您的{0}帐户。</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>使用服务登录不成功。</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>登录失败</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>或使用其他服务登录</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>使用本地帐户
</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>记住登录</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>创建一个新账户</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>确认密码</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>创建一个新账户</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>您好</value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>（不要忘记）注销</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>更多信息</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>在地图上</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>中心</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>地址</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>笔记</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>网站</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>电话号码</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>邮件</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>地铁</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>出口</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>活动</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>不活动</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>副标题</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>城市</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>国家</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>文件</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>上传图片</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>按名字查找</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>不允许</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>允许</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>，需要！</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>要导出</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>该记录是否导出到移动应用吗？</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>排序列表</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>按ABC排序</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>按修改时间排序</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>错误：图片网址无效！</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>控制面板</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>导出上</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>导出类型</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>拒绝访问</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>您似乎无权访问此部分。如果您认为可能是个错误，请联系支持。</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>你确定要导出吗？</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>导出成功完成！
</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>基本URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>中心名单</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>在该部分</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>所有</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>不该导出</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>已成功导出</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>应该导出</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>在该地区</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>按</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>分类</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>系统</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>超级用户</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>管理员</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>编辑者</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>来宾</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>属于</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>优先级</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>默认</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>子分类</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>父类别，要插入此成分</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>基础类别</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>新闻横幅</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>二级基础类别</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>上传微型图像</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>微型图像网址</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>类别</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>容量</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Thalion建议</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>我喜欢</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>单位</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>关键词</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>新</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>显示</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>任何类别</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>按代码</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>按类别</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>跟单员</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>重启</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>2基础类别</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>输入原因</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>公告的图像</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>身体</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>面子</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>否</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>我们的选择</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>忘记密码？</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>没有RU翻译</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Not Found Error</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>未知错误</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>我们链接</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>点击总数</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>你确定要删除吗？</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>保养</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>保养</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>错误：请检查以下字段的要求。</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>管理</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>用户</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>取消更改</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>不要保存</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>清单</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>您的记录仅在面板中可见。</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;Art of Foto&lt;/strong&gt; 控制面板</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>记住吗？</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>你还记得你的密码吗？</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>
重置密码</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>请检查您的电子邮件以重置密码。</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>重置密码</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>你有账户吗？</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>姓</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>同意&lt;strong&gt;条款&lt;/ strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>您必须接受条款和条件。</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>密码和确认密码不匹配。</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>{0}必须至少{2}字符长。</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>您输入的用户名或密码不正确。</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>电子邮件地址“{0}”已被使用</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>重置密码</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>请点击&lt;a href="{0}"&gt;连接&lt;/a&gt;重置密码。</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>新密码</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>请在下面输入您的新密码。</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>你还记得你以前的密码吗？</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>您的重置链接已过期。</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>您的新密码已设置。</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>类</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>隐私政策</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>发布时间</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>快速登录</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>或者输入您的凭据</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>使用社交网络登录</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>更改密码</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>账户管理</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>更改</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>双因素身份验证</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>您电话号码</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>已停用</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>已启用</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>管理</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>停用</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>使用社交网络登录</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>主题</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>关键</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>谢谢！</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>感谢您确认电子邮件。 您现在可以使用凭据登录。</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>您的帐户尚未分配到现有的Art of Foto客户端。请联系技术支持。</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>什么都没找到！</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>客户控制面板</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>谢谢你耐心等待。 我们很快就会回来。</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>抱歉，我们正在网站上做些工作</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>桌面</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>含有</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>产品要素</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>要素</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>描述RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>描述EN</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>描述FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>如果启用，则不会从面板中导出该记录。</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>您可以在下一个字段中上传文件或输入现有URL</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>无行动</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>在应用程序中打开产品</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>打开网络链接</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>字段“{0}”的值必须是唯一的</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>内容语言</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>输入2个字母的语言代码</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>公司信息</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>修改..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>按发布日期</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>排序优先级</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>快速导出</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>您确定要立即导出此部分吗？</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>导出成功完成。</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>载入中</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>推送消息</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>您信息</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>在程序内打开网址</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>简单的消息</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>现在发送</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>保存草稿</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>活跃用户</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>活动用户</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>非活动用户</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>英语语言不能为空。</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Android</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>开发人员</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>设备总数</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>保存草稿</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>尚未为您配置推送消息。</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>租户</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>客户端全局设置</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>更改已保存</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>按显示区域</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>有效设备数量</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>安装总数</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>创建密码</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>您的安全代码为{0}。 用它来登录Art of Foto 控制面板。</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>电子邮件地址或电话号码不正确。</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>找不到具有此电话号码的用户。 请注册。</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>请检查你的手机</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>我们已向您的电话号码发送验证码</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>已注册此电话号码的用户。</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>输入了错误的验证码。</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>待定确认</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>不赞成的</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>预订系统</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>前台</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>工作时间表</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>预订请求</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>预订对象</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>添加活动</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>插入事件名称</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>将事件拖到日历中</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>连接错误</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>最新的移动应用程序版本</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>过时的移动应用程序版本</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>开始</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>结局</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>整天</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2周</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>禁止预订。</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>您确定要删除此活动吗？</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>请稍候一会儿。。。</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>编辑活动</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>活动卡</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>需要确认</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>对象</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>服务类别</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>服务</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>服务</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>客户</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>细节</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>全名</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>GPS坐标X.</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>用于移动应用程序的必需唯一键。</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>未使用</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>在页面上显示</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>网址，产品代码（字段“关键”）等。</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>点击新闻时可能采取的行动</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>新闻文字</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>新闻所针对的语言区域</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>新闻图片</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>国际标题的语言</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>优先级越高，列表中的条目将显示得越高。</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>启用模块</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>注销用户</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>使此控制面板客户端的所有用户重新登录，以使视觉更改生效。</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>使用方法</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>参考代码</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>平台</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>邮件正文</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>收件人</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>控制面板中的客户端名称</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>颜色</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>价钱</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>工作日</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>工作时间来自</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>工作时间到</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>性别限制</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>午休时间到</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>客户，</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>在这里输入</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>团队</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>门将</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>教练员</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>成立日期</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>排名</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>按评级</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>Vkontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>更多信息</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>难度等级</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>“每小时”等。</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>价格细节</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>星期几</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>午休时间从</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>结束时间</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>团队</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>活动详情</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>活动</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>活动元素</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>组织</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>组织</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>活动类型</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>集会</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>锦标赛</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>其他</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>时间表类型</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>按星期几</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>固定日期</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>如果使用固定日期，则不使用星期几，反之亦然。</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>时间表</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>注销用户</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>出生日期</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>需要有效的用户名</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>工作时间</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>不明</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>等待确认</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>被拒绝</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>在档案中</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>预订请求</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>星期天</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>星期一</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>星期二</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>星期三</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>星期四</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>星期五</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>星期六</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>工作时间详细</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>自动确认预订</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>根据原始数据自动确认预订</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>需要指示预订的可用时间</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>明确指示每个对象的预订时间</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>画廊</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>您的名字</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>现在预订！</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>网上预定</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>回上一页</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>名</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>姓</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>串</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>更新数据..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>对于给定的条件，没有可用的时间。 请尝试更改以下条款：</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>哎呀！</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>客户ID</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0}在{1}等着您</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>在{0}等您</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>请等待{0}的确认</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>确认待定</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>面板</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>文本</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>移动应用</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>没空的时间</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>仅限预订</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>断面</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>文章</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>参看: </value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>价格模板</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>装饰</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>按记录</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>联系我们</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>路线查询</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>退回</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>对象</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>今天</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>明天</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>在{0}天</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>在{0}天</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>在{0}天</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>认证..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>您尝试了太多次，请在{0}分钟再试一次。</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>注册失败。 检查您是否输入了有效的电话号码，或稍后再试。</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>请检查您输入的数据是否有效。</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>预订失败了。</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>代码验证…</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>我们通过短信向您发送了验证码。请在下方输入以处理您的预订：</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>预订失败。 也许有人已预订了这个时间，请再试一次。</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>无法验证代码。</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>重新加载数据..</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} 在 {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>短信代码</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>取消了</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>等待确认</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>语言选择</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>点击或拖放文件到此字段中..</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>加载原始图片..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>打开</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>没有描述。</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>画廊</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>在移动应用程序没显示，用于在列表等中选择此项目的系统名称</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>我们等着您{0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>在 {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>文章</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>打开博客文章</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>发布日期</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>屏保标志 </value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>公司标志</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>显示在我们的联系人上</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>问题</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>等级</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>简单</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>正常</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>艰难</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>超级难</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>普通</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>头像</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>回答</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>回答</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>正确</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>问题</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>按等级</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR码图片网址</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>测验</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>一个问题的时间</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>测验</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>几秒钟内所有问题的时间</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>品牌</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>促销活动</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>包含标签</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>用标签排除</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>搜索此项目的关键字</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>请保存此记录以便能够添加子记录。</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>品牌</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>奖品</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>正确答案百分比</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>折扣</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>促销活动</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>积极</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>封闭</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>计划</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>其他</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>出口</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>按状态</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>载入中…</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>已成功</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>优惠券折扣</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>更多信息链接</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>名字</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>显示的问题数量</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>导入</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>*  - 包括所有问题。您还可以指定要包含的其他问题标签。</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>在app中打开促销活动</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>奖品总数</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>剩下奖品</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>简介</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>等待确认</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>拒绝了</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>网络错误</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>未知错误</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>用户</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>总请求数</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>请求</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>请求</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>订单</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>确认的请求</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>代码已经使用过</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>毫米</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>英寸</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>选择单位</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>关于该应用</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>联系</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>消息</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>中心名单</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>最新消息</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>您的中心</value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>改变地区</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>路线</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>优秀的 </value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>我们的中心</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>连接错误。 请稍后再试。</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>连接错误。 请稍后再试。</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>连接错误。 请稍后再试。</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>网络错误。请检查您的网络连接,然后重试。</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>我最喜欢的中心</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>欢迎您！</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>载入数据失败连接失败。请检查你的网络设置。</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>导航需要外部程序。</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>网站</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>我们在地图上</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>打电话 </value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>保存快速访问！</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>添加到收藏夹</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>重试</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>怎么找</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>正确的从收藏夹中移除中心吗？</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>清除收藏夹</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>现在，您可以从“收藏夹”部分快速访问此中心数据。</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>关于中心</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>我们需要访问您的GPS位置才能帮助找到我们。 现在启用访问？</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>您的GPS已关闭。 请启用它，以便我们能够为您提供帮助。</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>乘地铁：</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>联系我们</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>我们在地图上</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto 和著作权人</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>查找你的位置。。。</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>列表</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>地区</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>中心信息</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>专家信息</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>合伙人登入</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>如何找到我们</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>您可以将喜爱的中心添加到此部分以便快速访问。</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>寻找路线</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>用这个中心替换收藏夹中心？</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>查看完整的中心列表</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>寻找您的中心</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>祝贺您!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>好极了</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>连接错误。 请稍后再试。</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>中心</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>关于我们</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>在地图上</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>界面</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>没有文本底部菜单</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>启动程序时显示我的中心页面</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>首页</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>禁用背景动画以节省电池电量</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>退回中心列表</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>在程序启动时显示欢迎卡</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>更多</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>显示欢迎卡</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>现在开始</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>我们已发布更新，请更新应用程序！</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>再见！</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>无声推送通知</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>隐藏这个欢迎信息吗？</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>找到</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>添上</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>两次单击</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>跟随</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>单击部分图标上返回其根标题</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>向后</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>距离</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>在地图上</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>版本</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>产品目录</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>小类：</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>该部分的所有产品</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>如何使用</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>搜索结果</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>进一步阅读</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>搜索产品</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>流行</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>检视</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>您找了</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>请输入更多字符！</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>搜索中心</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>系统设置</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>以后</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>打开GPS</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>按距离排序</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>按字母排序</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>热门产品</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>您可以将目录中的产品添加到愿望清单中。该列表可用于以后与您的中心购物或与您的化妆师或朋友分享。</value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>愿望清单</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>确认列表中的删除</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>其他类别</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>转到目录</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>分享</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>转到分类</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>目录</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>我的愿望清单</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>清除清单</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>关于我们</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>家居护理产品仅通过THALION认证中心销售。</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>如果您的中心没有产品，请告知我们，我们将协助您购买。</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>哪里可以找到</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>联系我们</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>从愿望清单中删除吗?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>您确定要清除您的愿望清单吗？</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>我们需要您的GPS坐标来帮助您进行地理位置。</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>您的列表中有{0} {1}。</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>了解更多</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>已添加到愿望清单</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>再按一次退出程序</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>重启</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>查看目录</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>在目录中</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>在哪里找到</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>在左边</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>更多</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>也可以看看</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>产品目录</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>收藏夹</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>我的收藏</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>如果您希望我们找到离您最近的THALION中心，在下一个窗口积极回应。</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>您好！</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>再试一次</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>检查设置</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>订单处理中</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>关于该应用</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>时间</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>时间计算器</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>皮腔</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>皮腔延长度</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>焦距</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>在这里，你可以使用皮腔相机获得调整光圈的准确值</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>毫秒</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>。</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>Art of Foto项目成立于2011年。2015年，我们在圣彼得堡开设了Art of Foto画廊，暗室和大画幅摄影照相馆。画廊背后的想法是保护俄罗斯的摄影遗产，支持当代模拟摄影，以及发展艺术和技术水平。
 画廊收藏品具有历史和艺术价值的照片。目前，我们有着名大师制作的作品，如Valery Plotnikov，Boris Smelov，John Sexton，Leonid Bogdanov，Valentin Samarin，John Wimberly，Robert Doisneau等。
 该画廊的主要目标是在国内外推广有才华的俄罗斯摄影师。我们积极支持圣彼得堡和莫斯科的摄影师，他们的作品会被添加到俄罗斯的艺术遗产中。 
Art of Foto会员在俄罗斯和欧洲组织年度手绘黑白照片展览，旨在为全世界的鉴赏家和专家创造传统和现代俄罗斯摄影的正面形象。
</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>折叠</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>扩展</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C -按一次重置当前值，按两次完全重置
% - 与之前输入的操作结合使用。
例如：按+然后按％然后输入十进制数。
结果：您已将刚刚输入的百分比添加到现有时间。</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>您可以通过单击输入字段右侧的相应文本在毫米和英寸之间切换。</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>声音</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>启用小时输入</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>计时器从{0}开始</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>计时器已完成{0}</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>您可以通过单击输入字段右侧的相应文本在毫米和英寸之间切换。</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>没用过。</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>部分 </value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>毫升</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>显影液</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>搅拌显影液</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 mm</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>35mm换算</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>等效于35mm胶卷焦距对您的画幅。</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>画幅尺寸</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>容量内</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>按给定</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>结果（毫升）</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>A部分</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>B部分</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>水</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>根据指定数量计算显影液主要成分</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>自动计算数量为获取指定的显影液的数量</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>转换</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/Stop</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>选择标签（{0} / {1}最小{2}）</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>选择下方菜单项</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>主题</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>黑暗的</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>光亮的</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>来自AppoMobi的应用程序</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}，对角线{1}。
{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>准备好的</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>喜欢{0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>感谢您的反馈，我们将努力改进我们的应用程序！</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>感谢您在Google Play上对我们进行评分，我们将非常感谢！</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>吃点东西</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>已更正的曝光</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>溢出</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>无过滤器</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>必填</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>欢迎</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>发送到 {0} 设备</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>正在施工中</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>没有</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konyushennaya 街, 191186 圣彼得堡, 俄罗斯</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>敏, 我不知道你在说</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>公式不是由制作人传达的..。</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>制造商不建议使用此开发持续时间。</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>考虑到 Schwarzschild 效应的影响 ("Reciprocity effect") 的影响, 计算接触情况</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>史瓦西</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>警告

按制造商和类型平均照明与过滤器拍摄时的修正值。

建议您测试过滤器和薄膜, 以获得稳定和准确的结果。</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>您的结果是 {0:0.00}</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>由于缺少制造商的数据, 请使用我们自己的配方</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>根据生产商改正不是必要的在这个沥青</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>我们的新闻</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>2002年数据
根据制造商的说法,没有必要在一秒钟以下进行调整,因为超过一秒,我们使用我们自己的公式</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>到 2016 年的数据
制造商建议在开发中进行更正:
02:00 %
&amp;gt; 50 秒:-20%
&amp;gt; 20 分钟:-30%</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>该相机旨在实时查看底片。您可以更改滤镜，相机，并将图框保存到图库。</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>负片相机</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>相机</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>未经许可，该模块无法工作。请在系统设置中授权该应用程序或卸载该应用程序并从头开始安装以再次获得系统权限请求。</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>无权限</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>取景器</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>取景器</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>选拔</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>申请</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>镜头“ {0}”</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>变更格式</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>编辑预设</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>预设</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>电影</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>过滤器</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>该模块专为模拟取景器的近似模拟而设计。您可以用手指缩放屏幕。可以点击绿色值。</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>未添加镜头</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>格式</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>添加镜头（毫米）</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>屏幕常亮</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>调整</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>地理标记图片</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>地理标记照片所需的权限</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>使用 Art Of Foto 文件夹</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>打开</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>重试</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>开发表</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>开发表</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>博览会</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>光圈</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>快门</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>再次</value>
  </data>
  <data name="ExposureMeter" xml:space="preserve">
    <value>曝光表</value>
  </data>
</root>