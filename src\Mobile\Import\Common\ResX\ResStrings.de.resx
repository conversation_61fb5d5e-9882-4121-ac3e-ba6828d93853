﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>Das Projekt Art of Foto wurde 2011 gegründet und befasst sich tiefgehend mit der analogen schwar-weißen Fotografie.
2015 haben wir die Ausstellungshalle Art of Foto in S. Petersburg eröffnet. Hier sind auch das manuale Drucklabor und das Fotodtudio für großformatige Aufnahmen unterrgebracht.

Unsere Hauptaufgaben sind: Aufbewahrung und Popularisierung der fotografischen Nachlässe russischer Fotokünstler, Förderung der fotografischen Gegenwartskunst und ihrer künstlerischen wie technischen Weiterentwicklung.

Unsere Sammlungen enthalten Fotos, die einen geschichtlichen und künstlerischen Wert haben. In unseren Beständen sind Fotowerke von weit berühmten Künstlern beherbergt, wie Walerij Plotnikow, Boris Smelow, Lydmila Tabolina, Walentin Samarin, John Wimberley, John Sexton, Sergey Leontjew, Jan Schlegel, Alexander Kitajew u. m. A. Überdies werden in Sammlungen Ambrotypien, Dagguerotypien und historische Fotos aus dem XIX-XX Jh. aufbewahrt.

Die Mitglieder des Kunstverbandes Art of Foto stellen jährlich Sammlungen von schwarz-weißen manuel hergestellten Fotos in Russland und Europa aus mit dem Ziel, ein positives Bild und vorurteilsfreie Wahrnehmung der traditionellen und modernen russischen Fotokunst dem kündigen Publikum und Experten aus der ganzen Welt zu vermitteln.</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>ml</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>ms</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 mm</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>Sie können zwischen mm und Zoll wechseln, indem Sie auf den entsprechenden Text rechts neben dem Eingabefeld klicken.</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>Sie können zwischen mm und Zoll wechseln, indem Sie auf den entsprechenden Text rechts neben dem Eingabefeld klicken.</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>Faltenbalgverlängerung</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>Balg</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>Mischen Sie einen Entwickler</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>Entwickler</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>Hier können Sie einen genauen Wert erhalten, um die Blende Ihrer Balgenkamera einzustellen</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>Rahmenformat:</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>Klingen</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>Stundeneingabe aktivieren</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>Skin</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>Zeitrechner</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>Timer auf {0} beendet</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>Timer gestartet um {0}</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>innerhalb des Volumens</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>Vom Gegebenen</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>Ergebnis (ml)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>Teil A</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>Teil B</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>Wasser</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>Die Berechnung der Komponenten des Entwicklers basierend auf den angegebenen Volumina.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>Automatische Auswahl der Volumen, um die angegebene Entwicklermenge zu erhalten.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>Umgewandelt</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/Stop</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>Konvertieren Sie 35mm</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>teile</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C - einmal drücken, um den aktuellen Wert zurückzusetzen, zweimal für den vollständigen Reset
% - wird in Kombination mit der zuvor eingegebenen Operation verwendet.
Zum Beispiel: drücken Sie +, dann % und geben Sie eine Dezimalzahl ein.
Ergebnis: Sie haben die gerade eingegebenen Prozentzahlen zur vorhandenen Uhrzeit addiert.</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>Ihr Ergebnis ist {0:0.00}</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>Brennweite</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Einstellungen</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>Über uns</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>Niedriges Menü ohne Text</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Sprachauswahl</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Wählen Sie Ihre Tabs aus  ({0}/{1} min {2})</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto und die jeweiligen Inhaltseigentümer</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>Hier können Sie die Brennweite Ihres Objektivs in 35 mm-Äquivalent angeben.</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Auswahl unterer Menüpunkte</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Thema</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>Dark</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>Light</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>App von AppoMobi entwickelt</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, diagonale ist {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>Fertige Lösung</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>Mögen Sie {0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>Vielen Dank für Ihr Feedback, wir werden versuchen, unsere Anwendung zu verbessern!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>Bitte bewerten Sie uns bei GooglePlay, wir sind Ihnen sehr dankbar!</value>
  </data>
  <data name="Test" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>News hinzufügen</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>Nachrichten</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>gesamt</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>Nachrichten</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Kontakte</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Zurück zur Liste</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>en</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Sprache</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>En</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>Englisch</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Breite</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regionen</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Neu erstellen</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Karte-Zoom</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Map-Mittelpunkt Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>Map-Mittelpunkt X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Region in Mobile App</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese löschen möchten?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>oder</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Bereich hinzufügen</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Ihr Sicherheits-Code ist:</value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Kunst des Foto-Systemsteuerung</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Kunst, Foto: Erstellung des Kontos zu bestätigen</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art von Foto hat eine Anforderung zum Erstellen eines Benutzerkontos &lt;br&gt;empfangen
verwenden Ihre e-Mail-Adresse ({0}). &lt;br&gt;
&lt;br&gt;
Um den Vorgang fortzusetzen, ein Konto mit dieser e-Mail-Adresse zu erstellen, besuchen Sie die &lt;br&gt;
folgenden Link: &lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="blank" rel="noopener"&gt; {1}&lt;/a&gt; &lt;br&gt;
&lt;br&gt;
Wenn Sie nicht möchten, um ein Konto zu erstellen, oder wenn dieser Antrag in &lt;br&gt;
Fehler können Sie nur diese Meldung ignorieren. &lt;br&gt;
&lt;br&gt;
Wenn der obige Link nicht funktionieren, oder Sie andere Fragen bezüglich &lt;br&gt;haben
Ihr Konto kontaktieren Sie <NAME_EMAIL>. &lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Account-Erstellung</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>Wir haben Ihnen eine e-Mail an die folgende e-Mail Adresse: {0}.
Bitte folgen Sie den Anweisungen in dieser e-Mail, um die Registrierung abzuschließen.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Aktion</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Parameter</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Bild (Url)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Autor</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Bearbeitet von</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Bearbeitete Zeit</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Bildhöhe</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Breite des Bildes</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Vielen Dank für Ihre e-Mail-Adresse zu bestätigen. Bitte</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>Hier geht es zur Anmeldung</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>Sie haben erfolgreich mit authentifiziert.</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>Bitte geben Sie einen Benutzernamen für diese Website unter, und klicken Sie auf registrieren, um Anmeldung zu beenden.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Ihr {0}-Konto zu verknüpfen.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Erfolglosen Login mit Service.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Anmeldefehler</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Anmelden</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>Oder verwenden Sie einen anderen Dienst anmelden</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Verwenden Sie ein lokales Konto</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>erinnern Sie sich an mich</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>einloggen</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Ein neues Konto erstellen</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Bestätigen Sie Kennwort:</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Ein neues Konto erstellen</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Anmelden</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Hallo</value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(Nicht zu vergessen) Melden Sie sich ab</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>Okay</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>Mehr Info</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>Auf Karte</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Zentren</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notizen</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Webseite</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Metro</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>ExportNeeded von</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Zustand</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>aktiv</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inaktiv</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>ExportNeeded</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Untertitel</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Dateien</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Bild hochladen</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Suche nach name</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>Nicht erlaubt</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Erlaubt</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, gebraucht!</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>Exportiert werden</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Für mobile app oder nicht exportiert werden können.</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Liste sortieren</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>Sortieren nach Abc</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>Ausgabe Datum</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>Fehler: Bild-URL nicht gültig!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Control Panel</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Exporte</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Export für ausführen:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Exporttyp</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Zugriff verweigert</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>Es scheint, dass Sie nicht das Recht auf Zugang in diesem Abschnitt. Kontaktieren Sie Support, wenn Sie denken, dass es ein Fehler sein könnte.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exportieren</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese exportieren möchten?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Export erfolgreich abgeschlossen!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>Basis-URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Salon-Liste</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>innerhalb</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>Sollte nicht exportiert werden</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>Wurde auch exportiert</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>Exportiert werden soll</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>in der region</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>durch</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produkte</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Kategorien</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Super-User</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Editor</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Gast</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Elternteil</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priorität</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>In der Standardeinstellung</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Informationen</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Unterkategorien</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Übergeordnete Kategorie einfügen dieses Elements in</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Root-Kategorie</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>News-Slider</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Sekundäre Basiskategorie</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Mini-Bild hochladen</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>Mini-Bild-URL</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Kategorie</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Thalion Empfehlung</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>Ich mag</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Einheiten</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Schlüsselwörter</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Zeigen</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Suche...</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Jede Kategorie</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>Durch Code</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>Nach Kategorie</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Merchandiser</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Basiskategorie 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Geben Sie Grund</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Ausgewähltes Bild</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Körper</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Gesicht</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Unsere Wahl</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>Vergessen Passwort?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>Keine Übersetzung RU</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Nicht gefunden Fehler</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Unbekannter Fehler</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>verbinden</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Klicks insgesamt</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>Möchten Sie wirklich löschen</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Behandlung</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Behandlungen</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>Fehler: Bitte überprüfen Sie die Anforderungen für die folgenden Felder.</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Änderungen verwerfen</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Nicht speichern</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Des Herausgebers Notizen, internen Gebrauch</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;Kunst, Foto&lt;/strong&gt; Control Panel</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>erinnern Sie sich an mich?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Vergessen Sie Ihr Passwort?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Bitte überprüfen Sie Ihre e-Mail-Adresse, um Ihr Passwort zurückzusetzen.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Haben Sie ein Konto?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Konto registrieren</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nachname</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>Akzeptiere &lt;strong&gt;die Bedingungen&lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>Sie müssen die allgemeinen Geschäftsbedingungen akzeptieren.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>Das Kennwort und die Bestätigung Passwort stimmen nicht überein.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>Die {0} muss mindestens {2} Zeichen lang.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>Den Benutzernamen oder das eingegebene Kennwort ist falsch.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>E-Mail "{0}" ist bereits vergeben.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Bitte Ihr Passwort klicken Sie &lt;a href="{0}"&gt; hier&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Neues Passwort</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Bitte geben Sie Ihr neue Kennwort ein.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>Vergessen Sie Ihr alte Passwort?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Ihr Link zum Zurücksetzen des abgelaufen ist.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Ihr neue Passwort wurde festgelegt.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Klasse</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Datenschutz</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Veröffentlicht</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Melden Sie sich schnell mit</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>oder geben Sie Ihre Anmeldeinformationen</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>Externe Logins</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>nichts</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Ändern Sie Ihr Passwort</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Konto verwalten</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Veränderung</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Zwei-Faktor-Authentifizierung</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Ihre Telefonnummer</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Behinderte</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Aktiviert</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Aktivieren</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Verwalten</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Deaktivieren</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Entfernen</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Verwalten Sie Ihre externe logins</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Schlüssel</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>In der Nähe</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>Danke!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Vielen Dank für Ihre e-Mail-Adresse zu bestätigen. Jetzt können Sie Ihre Anmeldeinformationen anmelden.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Ihr Konto wurde noch nicht an eine vorhandene Art von Foto-Client zugewiesen. Kontaktieren Sie Ihr techsupport.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>Es wurde nichts gefunden!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Client-Kontrollfeld</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Vielen Dank für Ihre Geduld. Wir tun einiges auf der Website, die bald zurück sein wird.</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>Im Bau</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Leider tun wir einiges an Arbeit auf der Website</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>Desktop</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Enthält</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Produktelemente</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Komponenten</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Beschreibung RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Beschreibung de</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Beschreibung FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>Wird außerhalb der Systemsteuerung nicht exportiert werden, wenn eingeschaltet</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>Sie können eine Datei hochladen oder eine vorhandene URL in das nächste Feld eingeben</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>Keine Aktion</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Produkt in app</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Navigieren Sie zur url</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>Wert im Feld "{0}" muss eindeutig sein.</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Sprachen</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Geben Sie 2-Buchstaben-Sprachcodes</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unbekannt</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>Firmainfo</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Ändern...</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>Nach Veröffentlichungsdatum</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>Sortierpriorität</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>Auf Seite anzeigen</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Schneller Export</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>Möchten Sie in diesem Abschnitt nun exportieren?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>Export mit Erfolg abgeschlossen.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Push-Nachrichten</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Ihre News</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Navigieren Sie zur Url in app</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Einfache Botschaft</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Senden Sie jetzt</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Für später speichern</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Eingebundene Nutzer</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Aktive Nutzer</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Inaktive User</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>Text kann nicht für die englische Sprache leer sein.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Android</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Dev</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Geräte insgesamt</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Für später gespeichert</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Gesendet auf {0} Geräte</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Push-Nachrichten wurden noch nicht für Sie konfiguriert.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Mieter</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Client-globale Einstellungen</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Änderungen gespeichert</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>Nach Display-region</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Messagable Geräte insgesamt</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Installationen Gesamt</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Erstellen Sie ein Kennwort</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Ihr Sicherheitscode ist {0}. Verwenden Sie es auf Art von Foto Control Panel anmelden.</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Falsche e-Mail-Adresse oder Telefon-Nummer.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>Ein Benutzer mit dieser Telefonnummer wurde nicht gefunden. Bitte registrieren Sie sich.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Bitte überprüfen Sie Ihre Gerät</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>Wir haben einen Bestätigungscode an Ihre Nummer gesendet.</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>Benutzer mit dieser Telefonnummer bereits registriert.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Falscher Code eingegeben.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Bestätigt</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>Noch zu bestätigen</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Abgelehnt</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Buchungs-System</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Personal an der Rezeption</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Arbeitsprogramm</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Buchungsanfragen</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Buchung-Objekte</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Veranstaltung hinzufügen</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Event-Namen einfügen</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Drag &amp;amp; Drop-Ereignisse im Kalender</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Verbindungsfehler</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Neueste Version von mobile app</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Veraltete mobilen app-version</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>Ende</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Ganzen Tag</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 Wochen</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Buchung nicht gestattet</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>Möchten Sie dieses Ereignis wirklich löschen?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Nur für eine kurze Sekunde...</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Event bearbeiten</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Ereigniskarte</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>confirned</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>Bestätigung ausstehend</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Objekt</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Dienstleistungen-Kategorien</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Dienstleistungen</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Vollständiger Name</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>MapX</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Erforderliche eindeutige Schlüssel, verwendete in mobile app.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>Nicht verwendet.</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>URL, Produkt-Code (Feld "Schlüssel") etc...</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>Was tun, wenn Nachrichten Frame in app geklickt wird</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>Nachrichten-text</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Sprachraum die Nachricht wird in angezeigt.</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Bild, das in den Nachrichten gezeigt werden.</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>Internationale Titel Sprache</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>Je höher die Priorität der höheren Element in Liste erscheint</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Aktivierte Module</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Benutzer abmelden</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Alle Benutzer des Control Panels im Zusammenhang mit diesem Client Neustart damit die visuelle Änderungen wirksam zu machen.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>Gewusst wie: verwenden</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Referenz-code</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Plattform</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>Angezeigten Titel</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>Nachrichtentext</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Empfänger</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Clientnamen in der Systemsteuerung</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Farbe</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Arbeitstage</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Arbeitszeit von</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Arbeitszeit auf</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Sex-Beschränkung</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Ende zu brechen</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Kunden</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>Geben Sie hier</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Mannschaften</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Torhüter</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Trainer</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Seit</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Bewertung</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>Nach Bewertung</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>VKontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>Mehr Info</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Schwierigkeitsgrad</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"pro Stunde" etc...</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Preisdetails</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Tage der Woche</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Unbekannt</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Pause-start</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Startzeit</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>Endzeit</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Details zur Veranstaltung</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Veranstaltungen</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Veranstaltungen-Elemente</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organisationen</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Organisation</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Event-Typ</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Rally</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Meisterschaft</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Andere</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Zeitplantyp</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>Tage der Woche</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>Mit festen Terminen</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Zeitpläne</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Benutzer abmelden</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Geburtsdatum</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Gültigen Benutzernamen erforderlich</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Working Hours</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Unbekannt</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>BookingStatusPending</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>BookingStatusConfirmed</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>BookingStatusRejected</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>BookingStatusArchived</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Buchungsanfrage</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Sonntag</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Montag</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Dienstag</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Mittwoch</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Donnerstag</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Freitag</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Samstag</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Arbeitszeit Detaled</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Auto-bestätigen Buchungen</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>Auto/manuell</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Explizite buchbar Zeit</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Ob buchbar Zeit buchbar rechtzeitig für jedes Objekt verfügbar angegeben werden müssen</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>Buch</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Galerie</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Ihr Name</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>Buchen Sie jetzt!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Buchen Sie Online</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Familienname</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Schnur</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Aktualisieren von Daten...</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>Für den gegebenen Bedingungen gibt es keine verfügbaren Zeit. Versuchen Sie, die folgenden Bedingungen:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Hoppla!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Abgebrochen</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>Client-Id</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} wartet für Sie an {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>Wir werden für Sie warten auf {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Bitte warten auf Bestätigung für {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>Bestätigung ausstehend</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Bild</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Control Panel</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Streicher</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Zwicken, Mobile</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>Keine Zeit zur Verfügung</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>Für die Buchung nur</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Abschnitte</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>Siehe auch:</value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Preis-Maske</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Erscheinungsbilder</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>Von Noten</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>kontaktieren Sie uns</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Weg finden</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Objekte</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Heute</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Morgen</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>In {0} Tagen</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>In {0} Tagen</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>In {0} Tagen</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Authentifizierung...</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>Sie haben zu oft versucht, bitte versuchen Sie es erneut in {0} Minuten.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>Die Registrierung ist fehlgeschlagen. Bitte überprüfen Sie eine gültige Telefonnummer übermittelt haben, oder versuchen Sie es später erneut.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>Bitte überprüfen Sie die von die Ihnen eingegebenen Daten gültig ist.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>Buchung ist fehlgeschlagen.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Überprüfen Code...</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>Wir haben Ihnen einen Bestätigungscode per SMS geschickt. Bitte geben Sie diese unten, um Ihre Buchung zu bearbeiten:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>Buchung ist fehlgeschlagen. Vielleicht hat jemand bereits damals, bitte wiederholen genommen.</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>Fehler beim Überprüfen von Code.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Nachladen Buchungsdaten...</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} auf {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Code aus der SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Bestätigt</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Abgebrochen</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>Ausstehende</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Klicken Sie zum Hochladen oder Datei hierher ziehen...</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Original Bild wird geladen...</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>ansehen</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Ohne Beschreibung.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Galerien</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>In mobile app, System-Bezeichnung für die Auswahl dieses Punktes in Listen etc. nicht sichtbar</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>Wir erwarten Sie {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>Bei {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Blog</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Open-Blogartikel</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Release-Datum</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Splash-Logo</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Firmenimage</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Über unsere Kontakte angezeigt</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Frage</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Ebene</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>Einfach</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>Hart</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Superharte</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Antworten</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Antwort</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Richtig</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Quizz-Fragen</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>Von Ebene</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR-Code-Bild-Url</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Quizz</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>Zeit für eine Frage</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>Quiz</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>Zeit für alle Fragen in Sekunden</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Marken</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Promo Actons</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Mit Tags einschließen</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Mit Tags ausschließen</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Suchbegriffe</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Bitte speichern Sie diesen Datensatz um untergeordnete Datensätze hinzufügen können.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marke</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>Preise</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Richtigen Antworten Prozent</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Rabatt</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Promo-Aktion</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Offen</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Geschlossen</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>Eingehende</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Andere</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Ausfahrt</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Werden geladen...</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>Erfolg</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>Coupon-Prozent</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>Mehr Info-Link</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>Fragen insgesamt zu zeigen</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>* - umfasst sämtliche Fragen. Können auch andere benutzerdefinierte Tags einbezogen werden.</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>Offene Promoaction in app</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>Preise insgesamt</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>Preise Links</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>Ausstehende</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>Genehmigt</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>Verweigert</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>Netzwerkfehler</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>Unbekannter Fehler</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>Anforderungen insgesamt</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Anfrage</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Anfragen</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Oder</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>Bestätigten Anfragen</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>Abgelaufen</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>Millimeter</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>Zoll</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>Wählen Sie Einheiten</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>Über</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Kontakte</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>Nachrichten</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Finden Sie ein Zentrum</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Aktuelle Nachrichten</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>Ihr Salon</value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Region ändern</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Route zu finden</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Mein Salon</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>Unsere Zentren</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Verbindungsfehler. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Verbindungsfehler. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Verbindungsfehler. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Verbindungsfehler. Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>Okay</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>Meine Lieblings-Center</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>Herzlich Willkommen!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>Fehler beim Laden von Daten... 
Bitte überprüfen Sie Ihre Internetverbindung.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Bitte stellen Sie sicher, dass Sie eine Kartenanwendung installiert ist.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Website durchsuchen</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>Uns auf Karte anzeigen</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Rufen Sie</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>Für den schnellen Zugriff speichern!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Zu Favoriten hinzufügen</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Schließen Sie</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Anweisungen</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>Wirklich entfernen Zentrum aus Favoriten?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Aus Favoriten entfernen</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Jetzt können Sie schnell dieses Center-Daten aus dem Abschnitt "Favoriten" zugreifen.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>Über das Zentrum</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>Wir brauchen Zugang zu Ihrer GPS-Position in der Lage sein, um uns zu finden. Aktivieren Zugang jetzt?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Ihr GPS-Gerät ist ausgeschaltet. Bitte schalten sie für uns, Ihnen behilflich sein zu können.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>Mit der U-Bahn:</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Informationen</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>kontaktieren Sie uns</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Auf Karte anzeigen</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Finden Ihren Standort...</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Info Center</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Info für Spezialisten</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Partner-Login hier</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>So finden Sie uns</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>Ihr Lieblinge THALION-Center zu dieser Seite für den Schnellzugriff hinzufügen.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Route zu finden</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>Ersetzen Sie vorhandene Favoriten mit diesem?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>Volle Salon-Liste anzeigen</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Finden Sie Ihren Salon</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>Herzlichen Glückwunsch!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Yay cool</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Verbindungsfehler. Bitte versuchen Sie es später erneut.</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Zentren</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>Auf Karte</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Einstellungen</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Schnittstelle</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Seite mein Salon (Favorit) beim Programmstart anzeigen</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Homepage</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Hintergrundanimationen für Batterie speichern deaktivieren</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Zurück zur Liste der Salon</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Immer willkommen Tutorial beim Programmstart anzeigen</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>Noch mehr...</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Willkommen Folien zeigen</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>STARTEN SIE JETZT</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>Wir haben ein Update, aktualisieren Sie bitte die app!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>Bis bald!</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Push-Nachrichten Schweigen</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>Möchten Sie diese Willkommensnachricht verstecken?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>Finden</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>Satz</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Сlick doppelt</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>Folgen Sie</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>auf den Abschnitt-Symbol, um an seiner Wurzel zurückzukehren</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>Sortieren nach Km</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>Auf Karte</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Produkt-Katalog</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Sub-Kategorien:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>Alle Produkte aus Kategorie</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>THALION TIPP</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Suchergebnisse</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>Tippen Sie, um zu lesen</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Suche nach Produkten</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Suche</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>HEIß</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>ansehen</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>Sie haben gesucht:</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>Bitte geben Sie mehr Zeichen!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Suchcenter</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>System-Einstellungen</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Später</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>GPS einschalten</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>Sortiert nach der Entfernung</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>Sortiert nach Alphabet</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>HEIßE PRODUKTE</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>Produkte aus dem Katalog können zur Wunschliste hinzugefügt werden. 
Die Liste ist nützlich für später Einkaufen mit Ihrem Beauty-Center oder mit Ihrer Kosmetikerin oder Freunden zu teilen.</value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>Meine Wunschliste</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>Über uns</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Bitten Sie um Bestätigung, wenn Elemente aus Listen entfernt werden.</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>ANDERE KATEGORIEN</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Zum Katalog</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Freigeben</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Produkte</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>ZUR KATEGORIE GEHEN</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Ref.</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>Zum Katalog</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>Meine Wunschliste</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Liste löschen</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>Unsere Produkte für den privaten Gebrauch können in zertifizierten Zentren nur THALION erworben werden.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>Wenn Ihr Lieblinge THALION-Center einige der Produkte keinen auf Lager gewünschte würde, lass es uns wissen und wir helfen Ihnen mit dem Kauf.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Wo kaufen</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>kontaktieren Sie uns</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Position aus Merkliste entfernen?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>Möchten Sie Ihre Wunsch-Liste löschen?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>Wir bräuchten Ihre GPS-Koordinaten zu helfen, mit Geo-Position.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>Du hast {0} {1} in Ihrer Liste.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Weitere Informationen</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Artikel zur Wunschliste hinzugefügt</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Drücken Sie BACK erneut zum Beenden der Anwendung</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>Ansicht Katalog</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>Zur Übersicht</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Wo finde ich</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>Prev-Kategorie</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>Zeige mir mehr...</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>SIEHE AUCH</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Produkt-Katalog</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Favoriten</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>Meine Favoriten</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>Wenn Sie Sie würde wie zu finden THALION Zentren, die Ihnen am nächsten gelegenen bitte positiv sein im nächsten Fenster.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hallo</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Wiederholen</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Überprüfen Sie die Einstellungen</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Bearbeitung Ihrer Buchung...</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Über...</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>.</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konjuschennaja Straße 1, Sankt-Peterburg, Russland, 191186</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>Herzlich Willkommen</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>Zusammenbruch</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>Erweitern</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>Dies ist eine Entwickler-Modul-Hilfe.</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Erforderlich</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>Kein Filter</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Überlauf</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>Korrigierte Belichtung</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>Min.</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>Secs</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>Daten von 2016
Der Hersteller empfiehlt eine Korrektur zu machen, bei der Entwicklung:
02:00 %
&gt; 50 sec:-20 %
&gt; 20 min:-30 %</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>Einige Sachen zu essen</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>Wenn mit festen Terminen, Tage der Woche nicht verwendet werden und umgekehrt.</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>Formel wurde nicht vom Hersteller mitgeteilt...</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>Die Dauer dieser Entwicklung ist nicht vom Hersteller empfohlen.</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>Berechnung der Exposition, unter Berücksichtigung der Einfluss der Schwarzschild Effekt ("Reciprocity effect")</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>Reciprocity</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>WARNUNG

Der Wert der Änderungen bei Aufnahmen mit Filtern nach Hersteller und Typ gemittelt Beleuchtung. 

Empfehlen Sie, dass Sie testen, die Filter und Film, eine stabile und genaue Ergebnis zu erhalten.</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>Aufgrund des Fehlens von Angaben des Herstellers, mit unseren eigenen Formel</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>Laut Hersteller ist die Korrektur nicht notwendig in dieser Tonhöhe</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>Unsere nachrichten</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>Daten von 2002
Laut Hersteller ist es nicht notwendig mehr als eine Sekunde lang unter einer Sekunde Anpassungen wir unsere eigene Formel verwenden</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>Die Kamera ist so konzipiert, dass Negative in Echtzeit angezeigt werden. Sie können den Filter und die Kamera ändern und den Rahmen in der Galerie speichern.</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>Negative Kamera</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Kamera</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>Dieses Modul kann nicht ohne Berechtigungen arbeiten. Bitte autorisieren Sie die App in den Systemeinstellungen oder deinstallieren Sie die App und installieren Sie sie von Grund auf neu, um die Systemberechtigungsanforderung erneut zu erhalten.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>Keine Berechtigungen</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>Sucher</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>Sucher</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Auswahl</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Anwenden</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>Objektive für "{0}"</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>Format ändern</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>Voreinstellungen bearbeiten</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>Voreinstellung</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>Filme</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>Dieses Modul dient der ungefähren Simulation analoger Sucher. Sie können den Bildschirm mit Ihren Fingern zoomen. Grüne Werte können angetippt werden.</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>Kein Objektiv hinzugefügt</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>Linse hinzufügen (mm)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>Bildschirm immer an</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>Einstellung</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>Geotag-Bilder</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>Berechtigungen zum Geotagging von Fotos erforderlich</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Verwenden Sie den Art Of Foto-Ordner</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>Offen</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Schließen Sie</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>Die Entwicklungstabelle</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>Entwicklung</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>Exposition</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>Öffnung</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>Verschluss</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Wieder</value>
  </data>
  <data name="ExposureMeter" xml:space="preserve">
    <value>Expositionsmesser</value>
  </data>
</root>